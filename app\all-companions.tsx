import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Animated,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { supabase } from '../src/lib/supabase';
import { useAuth } from '../src/contexts/AuthContext';
import { useTheme } from '../src/contexts/ThemeContext';
import { COLORS } from '../src/lib/constants';
import { Ionicons } from '@expo/vector-icons';
import { assignCompanionImage, getLocalImagePath } from '../src/lib/imageUtils';

// Define the Companion interface
interface Companion {
  id: string;
  name: string;
  description: string; // Old persona field, now replaced with therapist_description
  companion_description?: string; // New concise description field
  image: string;
  specialties: string[];
  qualities?: Record<string, number>; // Therapist qualities with ratings
  rating: number; // Will be hidden in UI but kept in the interface for compatibility
  session_count: number;
  is_restricted?: boolean; // Whether this is a restricted companion
  has_access?: boolean; // Whether the current user has access to this companion
}

// Helper function to format companion name
const formatCompanionName = (companionId: string): string => {
  if (!companionId) return 'Unknown Companion';

  // Split by hyphens and handle each part
  const nameParts = companionId.split('-');

  // Capitalize each part of the name
  const formattedParts = nameParts.map(part =>
    part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
  );

  // Join parts with spaces
  return formattedParts.join(' ');
};

export default function AllCompanionsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { colors, isDark } = useTheme();
  const [companions, setCompanions] = useState<Companion[]>([]);
  const [filteredCompanions, setFilteredCompanions] = useState<Companion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Animation value
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Fetch companions from Supabase
  useEffect(() => {
    async function fetchCompanions() {
      try {
        setIsLoading(true);

        // 1. Fetch companion personas using the new function
        const { data: companionData, error: companionError } = await supabase
          .rpc('get_visible_companions', { user_id_input: user?.id || '00000000-0000-0000-0000-000000000000' });

        if (companionError) {
          throw companionError;
        }

        // 2. Fetch session counts for each companion using the new function
        // Use a valid UUID placeholder for guest users or when user is not logged in
        const { data: sessionData, error: sessionError } = await supabase
          .rpc('get_session_counts_by_companion', { user_id_param: user?.id || '00000000-0000-0000-0000-000000000000' });

        if (sessionError) {
          console.error('Error fetching session counts:', sessionError);
          // Continue with the process even if session count fetch fails
        }

        // Create a map of companion_id to session count
        const sessionCountMap: Record<string, number> = {};
        if (sessionData) {
          sessionData.forEach((item: { companion_id: string; count: number }) => {
            sessionCountMap[item.companion_id] = item.count;
          });
        }

        if (companionData) {
          // Map the data to match the Companion interface
          const mappedCompanions = companionData.map((item: any) => {
            // Format the name from companion_id (e.g., 'maya-thompson' -> 'Maya Thompson')
            const formattedName = formatCompanionName(item.companion_id);

            // Get the actual session count from the map, or default to 0
            const sessionCount = sessionCountMap[item.companion_id] || 0;

            // Create a companion object with the required properties
            const companion = {
              id: item.companion_id,
              name: formattedName,
              description: item.persona || item.specialty || 'Support Guide', // Keep for backward compatibility
              companion_description: item.companion_description || 'Support guide for personal growth', // New field
              image: '', // Will be set below
              is_restricted: item.is_restricted || false,
              has_access: item.has_access || true,
              specialties: item.best_for ? (Array.isArray(item.best_for) ? item.best_for : ['General Support']) : ['General Support'],
              qualities: item.qualities || {}, // Therapist qualities with ratings
              rating: 4.8, // Not displayed in UI anymore, but kept for data structure compatibility
              session_count: sessionCount // Real session count from database
            };

            // Assign the appropriate image using the imageUtils helper
            companion.image = item.image || item.companion_id;

            return companion;
          });

          setCompanions(mappedCompanions);
          setFilteredCompanions(mappedCompanions);
        }
      } catch (error) {
        console.error('Error fetching companions:', error);
      } finally {
        setIsLoading(false);
        // Fade in animation
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: Platform.OS !== 'web', // Don't use native driver on web
        }).start();
      }
    }

    fetchCompanions();
  }, []);

  // Handle companion selection
  const handleCompanionPress = (companion: Companion) => {
    if (user) {
      // User is logged in (either regular user or guest) - proceed to chat
      // Use setTimeout to ensure navigation happens after the current render cycle
      setTimeout(() => {
        // Use the companion_id directly as the companionId
        // Get the actual image path for this companion
        const imageSource = getLocalImagePath(companion.id);

        router.push({
          pathname: '/chat',
          params: {
            companionId: companion.id,
            companionName: companion.name,
            companionImageUrl: companion.id // Use the ID which will be resolved to the correct image path
          }
        });
      }, 50);
    } else {
      // User is not logged in - redirect to auth with companion info
      router.push({
        pathname: '/auth',
        params: {
          from: 'companion',
          companionId: companion.id,
          companionName: companion.name
        }
      });
    }
  };

  // Render companion item
  const renderCompanionItem = ({ item }: { item: Companion }) => {
    return (
      <TouchableOpacity
        onPress={() => handleCompanionPress(item)}
        style={[styles.companionCard, { backgroundColor: colors.background }]}
        disabled={item.is_restricted && !item.has_access}
      >
        {item.is_restricted && (
          <View style={styles.restrictedBadge}>
            <Text style={styles.restrictedBadgeText}>
              {item.has_access ? 'Premium' : 'Restricted'}
            </Text>
          </View>
        )}
        <Image
          source={getLocalImagePath(item.image)}
          style={styles.companionImage}
          resizeMode="cover"
        />
        <View style={styles.companionInfo}>
          <Text style={[styles.companionName, { color: colors.text }]}>
            {item.name}
          </Text>
          {/* Display the new therapist_description field */}
          <Text
            style={[styles.companionDescription, { color: colors.textSecondary }]}
            numberOfLines={2}
          >
            {item.companion_description || item.description}
          </Text>

          {/* Specialties */}
          <View style={styles.specialtiesContainer}>
            {item.specialties && item.specialties.slice(0, 3).map((specialty, index) => (
              <View
                key={index}
                style={[
                  styles.specialtyTag,
                  { backgroundColor: colors.surfaceLight }
                ]}
              >
                <Text style={[styles.specialtyText, { color: colors.textSecondary }]}>
                  {specialty}
                </Text>
              </View>
            ))}
          </View>

          {/* Companion Qualities */}
          {item.qualities && Object.keys(item.qualities).length > 0 && (
            <View style={styles.qualitiesContainer}>
              {Object.entries(item.qualities)
                .sort(([, valueA], [, valueB]) => (valueB as number) - (valueA as number))
                .slice(0, 3) // Show top 3 qualities
                .map(([quality, value], index) => (
                  <View key={index} style={styles.qualityItem}>
                    <Text style={[styles.qualityName, { color: colors.text }]}>{quality}</Text>
                    <View style={styles.qualityBarContainer}>
                      <View
                        style={[styles.qualityBar, {
                          backgroundColor: colors.accent,
                          width: `${(value as number) * 10}%`
                        }]}
                      />
                    </View>
                  </View>
              ))}
            </View>
          )}

          {/* Only Session Count - Rating Removed */}
          <View style={styles.statsContainer}>
            <View style={styles.sessionContainer}>
              <Ionicons name="chatbubble-outline" size={16} color={colors.textSecondary} />
              <Text style={[styles.sessionText, { color: colors.textSecondary }]}>
                {item.session_count} {item.session_count === 1 ? 'session' : 'sessions'}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Main component structure
  return (
    <View style={[styles.safeArea, { backgroundColor: colors.background }]}>
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        {/* Back Button */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.push('/')}
          >
            <Ionicons name="arrow-back" size={20} color={colors.text} />
            <Text style={[styles.backButtonText, { color: colors.text }]}>Back to Home</Text>
          </TouchableOpacity>
        </View>

        {/* Title */}
        <Text style={[styles.title, { color: colors.text }]}>
          Choose Your Companion
        </Text>

        {/* Loading Indicator */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.accent} />
          </View>
        ) : (
          <FlatList
            data={filteredCompanions}
            renderItem={renderCompanionItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonText: {
    marginLeft: 8,
    fontSize: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 20,
  },
  // Enhanced companion card with better spacing
  restrictedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: COLORS.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  restrictedBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  companionCard: Platform.select({
    ios: {
      flexDirection: 'row',
      borderRadius: 12,
      marginBottom: 16,
      padding: 12,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    android: {
      flexDirection: 'row',
      borderRadius: 12,
      marginBottom: 16,
      padding: 12,
      elevation: 2,
    },
    web: {
      flexDirection: 'row',
      borderRadius: 12,
      marginBottom: 16,
      padding: 12,
      boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    },
    default: {
      flexDirection: 'row',
      borderRadius: 12,
      marginBottom: 16,
      padding: 12,
    }
  }),
  companionImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 12,
  },
  companionInfo: {
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  companionName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  companionDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  qualitiesContainer: {
    marginBottom: 16,
  },
  qualityItem: {
    marginBottom: 8,
  },
  qualityName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  qualityBarContainer: {
    height: 6,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 3,
    overflow: 'hidden',
  },
  qualityBar: {
    height: '100%',
    borderRadius: 3,
  },
  specialtyTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 6,
  },
  specialtyText: {
    fontSize: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Rating container removed
  sessionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
  },
  sessionText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
});
